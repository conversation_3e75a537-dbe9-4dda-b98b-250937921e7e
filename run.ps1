#Requires -Version 5.1
<#
.SYNOPSIS
    Starts the simplified JoMaDe application.
.DESCRIPTION
    Simple script to start backend and frontend servers.
#>

Clear-Host
Write-Host "=== Starting JoMaDe Application (Simplified) ===" -ForegroundColor Cyan

# Configuration
$projectRoot = $PSScriptRoot
$backendDir = Join-Path $projectRoot "backend"
$frontendDir = Join-Path $projectRoot "frontend"
$venvPath = Join-Path $backendDir ".venv"

# Helper function to stop processes on ports
function Stop-ProcessOnPort {
    param ([int]$Port)
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($connection) {
            $processId = $connection.OwningProcess
            Write-Host "Stopping process on port $Port (PID: $processId)..." -ForegroundColor Yellow
            Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
        }
    }
    catch {
        # Ignore errors - port might not be in use
    }
}

# --- Backend Setup ---
Write-Host "`n--- Backend Setup ---" -ForegroundColor Magenta

# Check for Python virtual environment
$venvPythonPath = Join-Path (Join-Path $venvPath "Scripts") "python.exe"

if (-not (Test-Path $venvPath)) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    Push-Location $backendDir
    try {
        python -m venv .venv
        Write-Host "Virtual environment created." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to create virtual environment. Please ensure Python is installed."
        exit 1
    }
    finally {
        Pop-Location
    }
}

# Install minimal dependencies
if (-not (Test-Path (Join-Path (Join-Path $venvPath "Lib") (Join-Path "site-packages" "fastapi")))) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    Push-Location $backendDir
    try {
        & $venvPythonPath -m pip install fastapi uvicorn python-dotenv --quiet
        Write-Host "Backend dependencies installed." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install backend dependencies: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
}

# Start Backend Server
Write-Host "Starting backend server..." -ForegroundColor Yellow
Stop-ProcessOnPort -Port 8000

Push-Location $backendDir
try {
    $backendProcess = Start-Process -FilePath $venvPythonPath -ArgumentList "-m", "uvicorn", "api:app", "--reload", "--host", "0.0.0.0", "--port", "8000" -PassThru -WindowStyle Minimized
    Write-Host "Backend server started (PID: $($backendProcess.Id))" -ForegroundColor Green

    # Simple health check
    Start-Sleep -Seconds 3
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        Write-Host "Backend server is healthy!" -ForegroundColor Green
    }
    catch {
        Write-Host "Backend health check failed, but server may still be starting..." -ForegroundColor Yellow
    }
}
catch {
    Write-Error "Failed to start backend server: $($_.Exception.Message)"
    exit 1
}
finally {
    Pop-Location
}

# --- Frontend Setup ---
Write-Host "`n--- Frontend Setup ---" -ForegroundColor Magenta

# Install frontend dependencies if needed
if (-not (Test-Path (Join-Path $frontendDir "node_modules"))) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    Push-Location $frontendDir
    try {
        npm install --silent
        Write-Host "Frontend dependencies installed." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
}

# Start Frontend Server
Write-Host "Starting frontend server..." -ForegroundColor Yellow
Stop-ProcessOnPort -Port 3000

Push-Location $frontendDir
try {
    $frontendProcess = Start-Process -FilePath "npm" -ArgumentList "run", "dev" -PassThru -WindowStyle Minimized
    Write-Host "Frontend server started (PID: $($frontendProcess.Id))" -ForegroundColor Green

    # Simple health check
    Start-Sleep -Seconds 5
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        Write-Host "Frontend server is healthy!" -ForegroundColor Green
    }
    catch {
        Write-Host "Frontend health check failed, but server may still be starting..." -ForegroundColor Yellow
    }
}
catch {
    Write-Error "Failed to start frontend server: $($_.Exception.Message)"
    exit 1
}
finally {
    Pop-Location
}

# === Application Status ===
Write-Host "`n=== Application Status ===" -ForegroundColor Magenta
Write-Host "[OK] Backend: http://localhost:8000/docs" -ForegroundColor Green
Write-Host "[OK] Frontend: http://localhost:3000" -ForegroundColor Green

Write-Host "`nOpening http://localhost:3000 in browser..." -ForegroundColor Yellow
try {
    Start-Process "http://localhost:3000"
}
catch {
    Write-Warning "Could not open browser automatically."
}

Write-Host "`nJoMaDe application is running!" -ForegroundColor Cyan
Write-Host "Both servers are running in minimized windows." -ForegroundColor Gray
Write-Host "Press Enter to stop all servers..." -ForegroundColor Yellow
Read-Host

# === Shutdown ===
Write-Host "`nStopping servers..." -ForegroundColor Yellow

if ($frontendProcess -and -not $frontendProcess.HasExited) {
    Stop-Process -Id $frontendProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Host "Frontend server stopped." -ForegroundColor Green
}

if ($backendProcess -and -not $backendProcess.HasExited) {
    Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Host "Backend server stopped." -ForegroundColor Green
}

Write-Host "JoMaDe application stopped." -ForegroundColor Cyan