/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  output: 'standalone',
  // Enable static optimization for faster builds
  experimental: {
    optimizeCss: true,
  },
  // Configure redirects
  async redirects() {
    return [
      {
        source: '/',
        destination: '/settings',
        permanent: false,
      },
    ];
  },
  // Configure headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, must-revalidate',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
